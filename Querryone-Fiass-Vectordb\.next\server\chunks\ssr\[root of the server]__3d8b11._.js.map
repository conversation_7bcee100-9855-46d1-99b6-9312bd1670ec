{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/NewsCard.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { PiArrowRight } from \"react-icons/pi\";\r\n\r\ninterface NewsItem {\r\n  title?: string;\r\n  summary?: string;\r\n  url?: string;\r\n  image_url?: string;\r\n  formatted_date?: string;\r\n  source?: string;\r\n  category?: string;\r\n  published?: string;\r\n}\r\n\r\ninterface NewsCardProps {\r\n  news: NewsItem;\r\n}\r\n\r\nconst NewsCard: React.FC<NewsCardProps> = ({ news }) => {\r\n  const {\r\n    title = 'No title',\r\n    summary = 'No summary available',\r\n    url = '#',\r\n    image_url,\r\n    formatted_date,\r\n    published,\r\n    source = 'Unknown source',\r\n    category = 'all'\r\n  } = news;\r\n\r\n  // Format the date\r\n  const formatDate = (dateString: string | undefined) => {\r\n    if (!dateString) return 'No date';\r\n\r\n    try {\r\n      const date = new Date(dateString);\r\n      return date.toLocaleDateString('en-US', {\r\n        year: 'numeric',\r\n        month: 'short',\r\n        day: 'numeric'\r\n      });\r\n    } catch (e) {\r\n      return dateString || 'No date';\r\n    }\r\n  };\r\n\r\n  const displayDate = formatted_date || (published ? formatDate(published) : 'No date');\r\n  const imageUrl = image_url || 'https://via.placeholder.com/600x400?text=No+Image';\r\n  const truncatedSummary = summary\r\n    ? (summary.substring(0, 120) + (summary.length > 120 ? '...' : ''))\r\n    : 'No summary available';\r\n\r\n  // Format category for display (capitalize first letter)\r\n  const displayCategory = category !== 'all'\r\n    ? category.charAt(0).toUpperCase() + category.slice(1)\r\n    : null;\r\n\r\n  return (\r\n    <div className=\"border border-primaryColor/20 p-0 rounded-lg overflow-hidden hover:border-primaryColor/50 hover:shadow-md duration-300 cursor-pointer bg-white dark:bg-n0\">\r\n      <div\r\n        className=\"h-[200px] w-full bg-cover bg-center\"\r\n        style={{ backgroundImage: `url('${imageUrl}')` }}\r\n      ></div>\r\n      <div className=\"p-5\">\r\n        <div className=\"flex justify-between items-center mb-3\">\r\n          <div className=\"text-xs text-n100 dark:text-n30\">{displayDate}</div>\r\n          <span className=\"text-xs bg-primaryColor/10 text-primaryColor px-2 py-1 rounded-md\">{source}</span>\r\n        </div>\r\n        <h3 className=\"text-lg font-medium mb-3 line-clamp-2 dark:text-n30\">{title}</h3>\r\n\r\n        {displayCategory && (\r\n          <div className=\"mb-3\">\r\n            <span className=\"inline-block px-2 py-1 text-xs font-medium bg-primaryColor/10 text-primaryColor rounded-full\">\r\n              {displayCategory}\r\n            </span>\r\n          </div>\r\n        )}\r\n\r\n        <p className=\"text-sm text-n100 dark:text-n50 mb-4 line-clamp-3\">{truncatedSummary}</p>\r\n        <a\r\n          href={url}\r\n          target=\"_blank\"\r\n          rel=\"noopener noreferrer\"\r\n          className=\"text-primaryColor flex items-center text-sm font-medium hover:underline\"\r\n        >\r\n          Read more <PiArrowRight className=\"ml-1\" />\r\n        </a>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NewsCard;\r\n"], "names": [], "mappings": ";;;;AACA;;;AAiBA,MAAM,WAAoC,CAAC,EAAE,IAAI,EAAE;IACjD,MAAM,EACJ,QAAQ,UAAU,EAClB,UAAU,sBAAsB,EAChC,MAAM,GAAG,EACT,SAAS,EACT,cAAc,EACd,SAAS,EACT,SAAS,gBAAgB,EACzB,WAAW,KAAK,EACjB,GAAG;IAEJ,kBAAkB;IAClB,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QAExB,IAAI;YACF,MAAM,OAAO,IAAI,KAAK;YACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;gBACtC,MAAM;gBACN,OAAO;gBACP,KAAK;YACP;QACF,EAAE,OAAO,GAAG;YACV,OAAO,cAAc;QACvB;IACF;IAEA,MAAM,cAAc,kBAAkB,CAAC,YAAY,WAAW,aAAa,SAAS;IACpF,MAAM,WAAW,aAAa;IAC9B,MAAM,mBAAmB,UACpB,QAAQ,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM,GAAG,MAAM,QAAQ,EAAE,IAC/D;IAEJ,wDAAwD;IACxD,MAAM,kBAAkB,aAAa,QACjC,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC,KAClD;IAEJ,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,iBAAiB,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC;gBAAC;;;;;;0BAEjD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAmC;;;;;;0CAClD,8OAAC;gCAAK,WAAU;0CAAqE;;;;;;;;;;;;kCAEvF,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;oBAEpE,iCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCACb;;;;;;;;;;;kCAKP,8OAAC;wBAAE,WAAU;kCAAqD;;;;;;kCAClE,8OAAC;wBACC,MAAM;wBACN,QAAO;wBACP,KAAI;wBACJ,WAAU;;4BACX;0CACW,8OAAC,8IAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAK5C;uCAEe"}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/NewsCardSkeleton.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\nconst NewsCardSkeleton: React.FC = () => {\r\n  return (\r\n    <div className=\"border border-primaryColor/20 p-0 rounded-lg overflow-hidden bg-white dark:bg-n0\">\r\n      <div className=\"h-[200px] w-full bg-primaryColor/5 animate-pulse\"></div>\r\n      <div className=\"p-5\">\r\n        <div className=\"flex justify-between items-center mb-3\">\r\n          <div className=\"h-4 w-24 bg-primaryColor/5 animate-pulse rounded-md\"></div>\r\n          <div className=\"h-6 w-24 bg-primaryColor/5 animate-pulse rounded-md\"></div>\r\n        </div>\r\n        <div className=\"h-6 w-full bg-primaryColor/5 animate-pulse rounded-md mb-3\"></div>\r\n        <div className=\"h-4 w-full bg-primaryColor/5 animate-pulse rounded-md mb-1\"></div>\r\n        <div className=\"h-4 w-full bg-primaryColor/5 animate-pulse rounded-md mb-1\"></div>\r\n        <div className=\"h-4 w-3/4 bg-primaryColor/5 animate-pulse rounded-md mb-4\"></div>\r\n        <div className=\"h-5 w-32 bg-primaryColor/5 animate-pulse rounded-md\"></div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NewsCardSkeleton;\r\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,mBAA6B;IACjC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB;uCAEe"}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/components/CategoryTab.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\ninterface CategoryTabProps {\r\n  category: string;\r\n  isActive: boolean;\r\n  onClick: () => void;\r\n}\r\n\r\nconst CategoryTab: React.FC<CategoryTabProps> = ({ category, isActive, onClick }) => {\r\n  // Format category name for display (capitalize first letter)\r\n  const displayName = category.charAt(0).toUpperCase() + category.slice(1);\r\n  \r\n  return (\r\n    <button\r\n      onClick={onClick}\r\n      className={`px-4 py-2 text-sm font-medium rounded-full transition-colors duration-300 ${\r\n        isActive \r\n          ? 'bg-primaryColor text-white' \r\n          : 'bg-white dark:bg-n0 text-n700 dark:text-n30 hover:bg-primaryColor/10'\r\n      } border border-primaryColor/20`}\r\n    >\r\n      {displayName}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default CategoryTab;\r\n"], "names": [], "mappings": ";;;;;AAQA,MAAM,cAA0C,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE;IAC9E,6DAA6D;IAC7D,MAAM,cAAc,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC;IAEtE,qBACE,8OAAC;QACC,SAAS;QACT,WAAW,CAAC,0EAA0E,EACpF,WACI,+BACA,uEACL,8BAA8B,CAAC;kBAE/B;;;;;;AAGP;uCAEe"}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 365, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/services/api.ts"], "sourcesContent": ["import axios from 'axios';\r\n\r\n// Create an axios instance with default config\r\n// Configure base URL based on environment\r\nconst baseURL = process.env.NODE_ENV === 'development'\r\n  ? 'http://localhost:5010'\r\n  : 'http://localhost:5010';\r\n\r\nconst api = axios.create({\r\n  baseURL,\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n});\r\n\r\n// API functions\r\nexport const fetchNews = async (category: string = 'all') => {\r\n  try {\r\n    const response = await api.get('/api/news', {\r\n      params: { category }\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching news:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// FAISS-specific API functions\r\nexport const listEmbeddingModels = async () => {\r\n  try {\r\n    const response = await api.get('/api/list-embedding-models');\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching embedding models:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const listFaissCategories = async () => {\r\n  try {\r\n    const response = await api.post('/api/list-categories');\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching FAISS categories:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const checkFaissIndex = async (indexName: string, embedModel?: string) => {\r\n  try {\r\n    const response = await api.post('/api/check-index', {\r\n      index_name: indexName,\r\n      embed_model: embedModel\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error checking FAISS index:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Get current user's email from session storage\r\n */\r\nconst getCurrentUserEmail = (): string | null => {\r\n  try {\r\n    if (typeof window === 'undefined') return null;\r\n\r\n    // Try multiple sources for user email\r\n    const directEmail = localStorage.getItem('user_email') || sessionStorage.getItem('user_email');\r\n    if (directEmail) return directEmail;\r\n\r\n    // Try from user session data\r\n    const userSession = sessionStorage.getItem('resultUser');\r\n    if (userSession) {\r\n      const userData = JSON.parse(userSession);\r\n      return userData.email || userData.username || null;\r\n    }\r\n\r\n    return null;\r\n  } catch (error) {\r\n    console.error('Error getting current user email:', error);\r\n    return null;\r\n  }\r\n};\r\n\r\nexport const queryFaissIndex = async (query: string, indexName: string, k: number = 5, userEmail?: string) => {\r\n  try {\r\n    // Get user email if not provided\r\n    const emailToUse = userEmail || getCurrentUserEmail();\r\n\r\n    const requestBody: any = {\r\n      query,\r\n      index_name: indexName,\r\n      k\r\n    };\r\n\r\n    // Add user email for access validation if available\r\n    if (emailToUse) {\r\n      requestBody.user_email = emailToUse;\r\n    }\r\n\r\n    const response = await api.post('/api/query-faiss', requestBody);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error querying FAISS index:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const deleteFaissIndex = async (indexName: string) => {\r\n  try {\r\n    const response = await api.post('/api/delete-faiss-index', {\r\n      index_name: indexName\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error deleting FAISS index:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Data Management API functions\r\nexport const getIndexData = async (indexName: string, limit: number = 1000, offset: number = 0) => {\r\n  try {\r\n    const response = await api.post('/api/get-index-data', {\r\n      index_name: indexName,\r\n      limit,\r\n      offset\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error fetching index data:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const deleteIndexRows = async (indexName: string, rowIds: string[]) => {\r\n  try {\r\n    const response = await api.delete('/api/delete-index-rows', {\r\n      data: {\r\n        index_name: indexName,\r\n        row_ids: rowIds\r\n      }\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error deleting index rows:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport default api;\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAEA,+CAA+C;AAC/C,0CAA0C;AAC1C,MAAM,UAAU,uCACZ;AAGJ,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB;IACA,SAAS;QACP,gBAAgB;IAClB;AACF;AAGO,MAAM,YAAY,OAAO,WAAmB,KAAK;IACtD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,aAAa;YAC1C,QAAQ;gBAAE;YAAS;QACrB;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAGO,MAAM,sBAAsB;IACjC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAEO,MAAM,sBAAsB;IACjC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC;QAChC,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAEO,MAAM,kBAAkB,OAAO,WAAmB;IACvD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,oBAAoB;YAClD,YAAY;YACZ,aAAa;QACf;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAEA;;CAEC,GACD,MAAM,sBAAsB;IAC1B,IAAI;QACF,wCAAmC,OAAO;;QAE1C,sCAAsC;QACtC,MAAM;QAGN,6BAA6B;QAC7B,MAAM;IAOR,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;AACF;AAEO,MAAM,kBAAkB,OAAO,OAAe,WAAmB,IAAY,CAAC,EAAE;IACrF,IAAI;QACF,iCAAiC;QACjC,MAAM,aAAa,aAAa;QAEhC,MAAM,cAAmB;YACvB;YACA,YAAY;YACZ;QACF;QAEA,oDAAoD;QACpD,IAAI,YAAY;YACd,YAAY,UAAU,GAAG;QAC3B;QAEA,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,oBAAoB;QACpD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAEO,MAAM,mBAAmB,OAAO;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,2BAA2B;YACzD,YAAY;QACd;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAGO,MAAM,eAAe,OAAO,WAAmB,QAAgB,IAAI,EAAE,SAAiB,CAAC;IAC5F,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,uBAAuB;YACrD,YAAY;YACZ;YACA;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;AAEO,MAAM,kBAAkB,OAAO,WAAmB;IACvD,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,0BAA0B;YAC1D,MAAM;gBACJ,YAAY;gBACZ,SAAS;YACX;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF;uCAEe"}}, {"offset": {"line": 504, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 510, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/7th%20Sem/QuerryOne/Querryone-Fiass-Vectordb/app/%28with-layout%29/explore/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { PiArrowClockwise } from \"react-icons/pi\";\r\nimport NewsCard from \"@/components/NewsCard\";\r\nimport NewsCardSkeleton from \"@/components/NewsCardSkeleton\";\r\nimport CategoryTab from \"@/components/CategoryTab\";\r\nimport { fetchNews } from \"@/services/api\";\r\n\r\nfunction Explore() {\r\n  const [newsItems, setNewsItems] = useState([]);\r\n  const [categories, setCategories] = useState(['all', 'business', 'markets', 'economy', 'technology', 'banking', 'policy']);\r\n  const [currentCategory, setCurrentCategory] = useState('all');\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n\r\n  const loadNews = async (category = currentCategory) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      console.log(`Loading news for category: ${category}`);\r\n      const data = await fetchNews(category);\r\n\r\n      // Handle the new response format\r\n      if (data.news) {\r\n        console.log(`Received ${data.news.length} news items for category: ${category}`);\r\n        console.log(`Categories in response: ${data.categories?.join(', ')}`);\r\n\r\n        // Log the categories of the received news items\r\n        const categoryDistribution = data.news.reduce((acc, item) => {\r\n          acc[item.category] = (acc[item.category] || 0) + 1;\r\n          return acc;\r\n        }, {});\r\n        console.log('Category distribution:', categoryDistribution);\r\n\r\n        setNewsItems(data.news);\r\n\r\n        // Update categories if provided by the API\r\n        if (data.categories && Array.isArray(data.categories)) {\r\n          setCategories(data.categories);\r\n        }\r\n\r\n        // Update current category if provided\r\n        if (data.current_category) {\r\n          setCurrentCategory(data.current_category);\r\n        }\r\n      } else {\r\n        // Fallback for backward compatibility\r\n        console.log('Using fallback response format');\r\n        setNewsItems(data);\r\n      }\r\n    } catch (err) {\r\n      console.error('Error loading news:', err);\r\n      setError('Failed to load news. Please try again later.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Handle category change\r\n  const handleCategoryChange = (category: string) => {\r\n    if (category !== currentCategory) {\r\n      setCurrentCategory(category);\r\n      loadNews(category);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    loadNews();\r\n  }, []);\r\n  return (\r\n    <div className=\"w-full overflow-auto flex justify-center items-start py-8\">\r\n      <div className=\"flex flex-col justify-center items-center px-6 max-w-[1080px] w-full\">\r\n        <div className=\"text-center mb-8\">\r\n          <p className=\"text-3xl font-semibold text-n700 dark:text-n30\">\r\n            Financial Dashboard\r\n          </p>\r\n          <p className=\"pt-3 max-w-[600px] text-n100 dark:text-n50\">\r\n            Stay informed with the latest Indian financial news\r\n          </p>\r\n        </div>\r\n        {/* News Section */}\r\n        <div className=\"w-full\">\r\n          <div className=\"flex flex-col justify-center items-center text-center gap-3 mb-6\">\r\n            <p className=\"text-2xl text-n700 font-semibold dark:text-n30\">\r\n              Top Financial News\r\n            </p>\r\n            <p className=\"text-n100 dark:text-n50 max-w-[600px]\">\r\n              Stay updated with the latest financial news and market trends from trusted sources\r\n            </p>\r\n          </div>\r\n\r\n          {/* Category Navigation Bar */}\r\n          <div className=\"w-full mb-8\">\r\n            <div className=\"flex flex-wrap justify-center items-center gap-2 pb-2 overflow-x-auto\">\r\n              {categories.map((category) => (\r\n                <CategoryTab\r\n                  key={category}\r\n                  category={category}\r\n                  isActive={currentCategory === category}\r\n                  onClick={() => handleCategoryChange(category)}\r\n                />\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\r\n            {loading ? (\r\n              // Show skeletons while loading\r\n              Array(6).fill().map((_, index) => (\r\n                <NewsCardSkeleton key={index} />\r\n              ))\r\n            ) : error ? (\r\n              // Show error message if there's an error\r\n              <div className=\"col-span-full text-center py-10 bg-white dark:bg-n0 rounded-lg border border-primaryColor/20 p-6\">\r\n                <h3 className=\"text-xl mb-3 text-n700 dark:text-n30\">Error loading news</h3>\r\n                <p className=\"text-n100 dark:text-n50\">{error}</p>\r\n              </div>\r\n            ) : newsItems.length === 0 ? (\r\n              // Show message if no news items\r\n              <div className=\"col-span-full text-center py-10 bg-white dark:bg-n0 rounded-lg border border-primaryColor/20 p-6\">\r\n                <h3 className=\"text-xl mb-3 text-n700 dark:text-n30\">No news found</h3>\r\n                <p className=\"text-n100 dark:text-n50\">Check back later for updates.</p>\r\n              </div>\r\n            ) : (\r\n              // Show news items\r\n              newsItems.map((item, index) => (\r\n                <NewsCard key={index} news={item} />\r\n              ))\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"w-full pt-10 text-center\">\r\n            <div className=\"flex flex-col items-center\">\r\n              {currentCategory !== 'all' && (\r\n                <p className=\"text-sm text-n100 dark:text-n50 mb-2\">\r\n                  Showing news for category: <span className=\"font-medium text-primaryColor\">{currentCategory.charAt(0).toUpperCase() + currentCategory.slice(1)}</span>\r\n                </p>\r\n              )}\r\n              <button\r\n                className=\"border border-primaryColor rounded-full py-3 px-8 text-center text-primaryColor text-sm font-medium inline-flex items-center justify-center gap-2 hover:bg-primaryColor hover:text-white transition-colors duration-300\"\r\n                onClick={() => loadNews(currentCategory)}\r\n                disabled={loading}\r\n              >\r\n                <PiArrowClockwise className={loading ? \"animate-spin\" : \"\"} />\r\n                {loading ? 'Loading...' : 'Refresh News'}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Explore;\r\n"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AACA;AACA;AAJA;AAFA;;;;;;;;AAQA,SAAS;IACP,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;QAAO;QAAY;QAAW;QAAW;QAAc;QAAW;KAAS;IACzH,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,WAAW,OAAO,WAAW,eAAe;QAChD,IAAI;YACF,WAAW;YACX,SAAS;YACT,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,UAAU;YACpD,MAAM,OAAO,MAAM,CAAA,GAAA,+GAAA,CAAA,YAAS,AAAD,EAAE;YAE7B,iCAAiC;YACjC,IAAI,KAAK,IAAI,EAAE;gBACb,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC,MAAM,CAAC,0BAA0B,EAAE,UAAU;gBAC/E,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,KAAK,UAAU,EAAE,KAAK,OAAO;gBAEpE,gDAAgD;gBAChD,MAAM,uBAAuB,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK;oBAClD,GAAG,CAAC,KAAK,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,IAAI,CAAC,IAAI;oBACjD,OAAO;gBACT,GAAG,CAAC;gBACJ,QAAQ,GAAG,CAAC,0BAA0B;gBAEtC,aAAa,KAAK,IAAI;gBAEtB,2CAA2C;gBAC3C,IAAI,KAAK,UAAU,IAAI,MAAM,OAAO,CAAC,KAAK,UAAU,GAAG;oBACrD,cAAc,KAAK,UAAU;gBAC/B;gBAEA,sCAAsC;gBACtC,IAAI,KAAK,gBAAgB,EAAE;oBACzB,mBAAmB,KAAK,gBAAgB;gBAC1C;YACF,OAAO;gBACL,sCAAsC;gBACtC,QAAQ,GAAG,CAAC;gBACZ,aAAa;YACf;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,uBAAuB;YACrC,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,yBAAyB;IACzB,MAAM,uBAAuB,CAAC;QAC5B,IAAI,aAAa,iBAAiB;YAChC,mBAAmB;YACnB,SAAS;QACX;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IACL,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAiD;;;;;;sCAG9D,8OAAC;4BAAE,WAAU;sCAA6C;;;;;;;;;;;;8BAK5D,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAiD;;;;;;8CAG9D,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;sCAMvD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,0HAAA,CAAA,UAAW;wCAEV,UAAU;wCACV,UAAU,oBAAoB;wCAC9B,SAAS,IAAM,qBAAqB;uCAH/B;;;;;;;;;;;;;;;sCASb,8OAAC;4BAAI,WAAU;sCACZ,UACC,+BAA+B;4BAC/B,MAAM,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,sBACtB,8OAAC,+HAAA,CAAA,UAAgB,MAAM;;;;4CAEvB,QACF,yCAAyC;0CACzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC;wCAAE,WAAU;kDAA2B;;;;;;;;;;;uCAExC,UAAU,MAAM,KAAK,IACvB,gCAAgC;0CAChC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;uCAGzC,kBAAkB;4BAClB,UAAU,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,uHAAA,CAAA,UAAQ;oCAAa,MAAM;mCAAb;;;;;;;;;;sCAKrB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;oCACZ,oBAAoB,uBACnB,8OAAC;wCAAE,WAAU;;4CAAuC;0DACvB,8OAAC;gDAAK,WAAU;0DAAiC,gBAAgB,MAAM,CAAC,GAAG,WAAW,KAAK,gBAAgB,KAAK,CAAC;;;;;;;;;;;;kDAGhJ,8OAAC;wCACC,WAAU;wCACV,SAAS,IAAM,SAAS;wCACxB,UAAU;;0DAEV,8OAAC,8IAAA,CAAA,mBAAgB;gDAAC,WAAW,UAAU,iBAAiB;;;;;;4CACvD,UAAU,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C;uCAEe"}}, {"offset": {"line": 812, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}